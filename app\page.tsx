"use client"

import { useState } from "react"
import { motion, AnimatePresence } from "framer-motion"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Checkbox } from "@/components/ui/checkbox"
import { Badge } from "@/components/ui/badge"
import { Separator } from "@/components/ui/separator"
import { User, MapPin, Calendar, Clock, CreditCard, Phone, Mail, Building, CheckCircle } from "lucide-react"

export default function SouthDocInterface() {
  const [activeTab, setActiveTab] = useState(1)
  const [selectedTimeSlot, setSelectedTimeSlot] = useState("")
  const [selectedClinic, setSelectedClinic] = useState("")

  const clinics = [
    {
      id: "bently",
      name: "Ben<PERSON>",
      address: "A Clinic Along Which Someone Or Something Moves...",
      status: "available",
    },
    { id: "limerick", name: "Limerick City Mobile", address: "Left from The Green Bed", status: "available" },
    { id: "nenagh", name: "Nenagh", address: "Beds", status: "available" },
    { id: "shannon", name: "Shannon Tr", address: "Common To Come purchase Glentrees", status: "available" },
    { id: "fatlana", name: "Fatlana Centre", address: "Add to list", status: "available" },
  ]

  const timeSlots = [
    "09:20 - 09:30",
    "09:40 - 09:50",
    "10:30 - 10:40",
    "10:30 - 10:40",
    "10:40 - 10:50",
    "11:00 - 11:10",
    "11:10 - 11:20",
    "11:30 - 11:40",
    "12:10 - 12:20",
    "12:30 - 12:40",
    "12:50 - 13:00",
    "13:20 - 13:30",
    "13:40 - 13:50",
    "14:40 - 14:50",
    "14:50 - 15:00",
    "15:20 - 15:30",
    "16:00 - 16:10",
    "16:40 - 16:20",
    "16:30 - 16:40",
    "16:40 - 16:50",
  ]

  const tabVariants = {
    hidden: { opacity: 0, x: 20 },
    visible: { opacity: 1, x: 0 },
    exit: { opacity: 0, x: -20 },
  }

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
      },
    },
  }

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: { opacity: 1, y: 0 },
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <motion.header
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6 }}
        className="bg-teal-600 text-white px-6 py-4"
      >
        <div className="max-w-7xl mx-auto flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <div className="bg-white text-teal-600 px-4 py-2 rounded font-bold text-lg">SouthDoc</div>
          </div>
          <div className="text-right">
            <p className="text-sm font-medium">SouthDoc is a GP out of hours service</p>
            <p className="text-xs opacity-90">for medical issues that cannot wait for daytime practice</p>
          </div>
        </div>
      </motion.header>

      {/* Tab Navigation */}
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ delay: 0.2, duration: 0.5 }}
        className="bg-white border-b"
      >
        <div className="max-w-7xl mx-auto px-6">
          <div className="flex justify-center space-x-8 py-4">
            <button
              onClick={() => setActiveTab(1)}
              className={`flex items-center justify-center w-10 h-10 rounded-full font-semibold transition-all duration-300 ${
                activeTab === 1 ? "bg-teal-600 text-white shadow-lg" : "bg-gray-200 text-gray-600 hover:bg-gray-300"
              }`}
            >
              1
            </button>
            <button
              onClick={() => setActiveTab(2)}
              className={`flex items-center justify-center w-10 h-10 rounded-full font-semibold transition-all duration-300 ${
                activeTab === 2 ? "bg-teal-600 text-white shadow-lg" : "bg-gray-200 text-gray-600 hover:bg-gray-300"
              }`}
            >
              2
            </button>
          </div>
        </div>
      </motion.div>

      {/* Main Content */}
      <div className="max-w-7xl mx-auto px-6 py-8">
        <AnimatePresence mode="wait">
          {activeTab === 1 && (
            <motion.div
              key="tab1"
              variants={tabVariants}
              initial="hidden"
              animate="visible"
              exit="exit"
              transition={{ duration: 0.5 }}
            >
              <motion.div
                variants={containerVariants}
                initial="hidden"
                animate="visible"
                className="grid grid-cols-1 lg:grid-cols-2 gap-8"
              >
                {/* Patient Information */}
                <motion.div variants={itemVariants}>
                  <Card className="shadow-lg border-0">
                    <CardHeader className="bg-gradient-to-r from-teal-50 to-teal-100 border-b">
                      <CardTitle className="flex items-center space-x-2 text-teal-700">
                        <User className="w-5 h-5" />
                        <span>Patient Information</span>
                      </CardTitle>
                      <p className="text-sm text-gray-600">Tell us about yourself</p>
                    </CardHeader>
                    <CardContent className="p-6 space-y-6">
                      <div>
                        <Label htmlFor="consultation-reason" className="text-sm font-medium text-gray-700">
                          Reason for Consultation *
                        </Label>
                        <div className="mt-2">
                          <Textarea
                            id="consultation-reason"
                            placeholder="Type your symptoms or reason for consultation..."
                            className="min-h-[100px] border-gray-300 focus:border-teal-500 focus:ring-teal-500"
                          />
                        </div>
                        <div className="mt-2 text-center text-gray-500 text-sm">OR</div>
                        <Button
                          variant="outline"
                          className="w-full mt-2 text-teal-600 border-teal-200 hover:bg-teal-50 bg-transparent"
                        >
                          Clear Selection
                        </Button>
                      </div>

                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                          <Label htmlFor="first-name" className="text-sm font-medium text-gray-700">
                            First Name *
                          </Label>
                          <Input
                            id="first-name"
                            placeholder="Hamza"
                            className="mt-1 border-gray-300 focus:border-teal-500 focus:ring-teal-500"
                          />
                        </div>
                        <div>
                          <Label htmlFor="last-name" className="text-sm font-medium text-gray-700">
                            Last Name *
                          </Label>
                          <Input
                            id="last-name"
                            placeholder="Arshad"
                            className="mt-1 border-gray-300 focus:border-teal-500 focus:ring-teal-500"
                          />
                        </div>
                      </div>

                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                          <Label htmlFor="dob" className="text-sm font-medium text-gray-700">
                            Date of Birth *
                          </Label>
                          <Input
                            id="dob"
                            type="date"
                            defaultValue="2000-08-31"
                            className="mt-1 border-gray-300 focus:border-teal-500 focus:ring-teal-500"
                          />
                        </div>
                        <div>
                          <Label htmlFor="gender" className="text-sm font-medium text-gray-700">
                            Gender *
                          </Label>
                          <Select>
                            <SelectTrigger className="mt-1 border-gray-300 focus:border-teal-500 focus:ring-teal-500">
                              <SelectValue placeholder="Male" />
                            </SelectTrigger>
                            <SelectContent>
                              <SelectItem value="male">Male</SelectItem>
                              <SelectItem value="female">Female</SelectItem>
                              <SelectItem value="other">Other</SelectItem>
                            </SelectContent>
                          </Select>
                        </div>
                      </div>

                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                          <Label htmlFor="gms-number" className="text-sm font-medium text-gray-700">
                            GMS Number
                          </Label>
                          <Input
                            id="gms-number"
                            placeholder="w0787856"
                            className="mt-1 border-gray-300 focus:border-teal-500 focus:ring-teal-500"
                          />
                        </div>
                        <div>
                          <Label htmlFor="gms-expiry" className="text-sm font-medium text-gray-700">
                            GMS Expiry
                          </Label>
                          <Input
                            id="gms-expiry"
                            type="date"
                            defaultValue="2024-10-31"
                            className="mt-1 border-gray-300 focus:border-teal-500 focus:ring-teal-500"
                          />
                          <p className="text-xs text-red-500 mt-1">⚠ GMS has expired</p>
                        </div>
                      </div>

                      <div>
                        <Label htmlFor="contact-number" className="text-sm font-medium text-gray-700">
                          Contact Number *
                        </Label>
                        <div className="mt-1 flex">
                          <span className="inline-flex items-center px-3 rounded-l-md border border-r-0 border-gray-300 bg-gray-50 text-gray-500 text-sm">
                            IE +353
                          </span>
                          <Input
                            id="contact-number"
                            placeholder="083123467"
                            className="rounded-l-none border-gray-300 focus:border-teal-500 focus:ring-teal-500"
                          />
                        </div>
                        <p className="text-xs text-amber-600 mt-1">⚠ Please enter a valid phone number</p>
                      </div>

                      <div>
                        <Label htmlFor="email" className="text-sm font-medium text-gray-700">
                          Email Address
                        </Label>
                        <Input
                          id="email"
                          type="email"
                          placeholder="<EMAIL>"
                          className="mt-1 border-gray-300 focus:border-teal-500 focus:ring-teal-500"
                        />
                      </div>

                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                          <Label htmlFor="general-practitioner" className="text-sm font-medium text-gray-700">
                            General Practitioner *
                          </Label>
                          <Input
                            id="general-practitioner"
                            className="mt-1 border-gray-300 focus:border-teal-500 focus:ring-teal-500"
                          />
                        </div>
                        <div>
                          <Label htmlFor="surgery-clinic" className="text-sm font-medium text-gray-700">
                            Surgery Clinic *
                          </Label>
                          <Button
                            variant="outline"
                            className="w-full mt-1 justify-start text-teal-600 border-teal-200 hover:bg-teal-50 bg-transparent"
                          >
                            Clear Selection
                          </Button>
                        </div>
                      </div>

                      <div className="flex items-center space-x-2">
                        <Checkbox id="bypass-gp" className="border-gray-300" />
                        <Label htmlFor="bypass-gp" className="text-sm text-gray-700">
                          Bypass GP
                        </Label>
                      </div>

                      <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                        <p className="text-sm text-blue-700">Outcome of the appointment won't send to your GP</p>
                      </div>
                    </CardContent>
                  </Card>
                </motion.div>

                {/* Location */}
                <motion.div variants={itemVariants}>
                  <Card className="shadow-lg border-0">
                    <CardHeader className="bg-gradient-to-r from-teal-50 to-teal-100 border-b">
                      <CardTitle className="flex items-center space-x-2 text-teal-700">
                        <MapPin className="w-5 h-5" />
                        <span>Location</span>
                      </CardTitle>
                      <p className="text-sm text-gray-600">Enter your location details</p>
                    </CardHeader>
                    <CardContent className="p-6 space-y-6">
                      <div>
                        <Button
                          variant="outline"
                          className="w-full text-teal-600 border-teal-200 hover:bg-teal-50 bg-transparent"
                        >
                          Clear Selection
                        </Button>
                        <div className="mt-4 relative">
                          <Input
                            placeholder="Search Address or Eircode"
                            className="pl-10 border-gray-300 focus:border-teal-500 focus:ring-teal-500"
                          />
                          <MapPin className="w-4 h-4 absolute left-3 top-3 text-gray-400" />
                        </div>
                        <p className="text-xs text-gray-500 mt-2">📍 Current location detected and populated below.</p>
                      </div>

                      <div>
                        <h3 className="font-medium text-gray-900 mb-4">Current Location</h3>
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                          <div>
                            <Label htmlFor="current-building" className="text-sm font-medium text-gray-700">
                              Building *
                            </Label>
                            <Input
                              id="current-building"
                              defaultValue="4"
                              className="mt-1 border-gray-300 focus:border-teal-500 focus:ring-teal-500"
                            />
                          </div>
                          <div>
                            <Label htmlFor="current-street" className="text-sm font-medium text-gray-700">
                              Street *
                            </Label>
                            <Input
                              id="current-street"
                              defaultValue="28"
                              className="mt-1 border-gray-300 focus:border-teal-500 focus:ring-teal-500"
                            />
                          </div>
                        </div>
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-4">
                          <div>
                            <Label htmlFor="current-area" className="text-sm font-medium text-gray-700">
                              Area
                            </Label>
                            <Input
                              id="current-area"
                              defaultValue="ZARAJ TOWN"
                              className="mt-1 border-gray-300 focus:border-teal-500 focus:ring-teal-500"
                            />
                          </div>
                          <div>
                            <Label htmlFor="current-city" className="text-sm font-medium text-gray-700">
                              City
                            </Label>
                            <Input
                              id="current-city"
                              defaultValue="Rawalpindi"
                              className="mt-1 border-gray-300 focus:border-teal-500 focus:ring-teal-500"
                            />
                          </div>
                        </div>
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-4">
                          <div>
                            <Label htmlFor="current-country" className="text-sm font-medium text-gray-700">
                              Country
                            </Label>
                            <Input
                              id="current-country"
                              defaultValue="Pakistan"
                              className="mt-1 border-gray-300 focus:border-teal-500 focus:ring-teal-500"
                            />
                          </div>
                          <div>
                            <Label htmlFor="current-eircode" className="text-sm font-medium text-gray-700">
                              EirCode
                            </Label>
                            <div className="flex items-center space-x-2 mt-1">
                              <Input
                                id="current-eircode"
                                defaultValue="45900"
                                className="border-gray-300 focus:border-teal-500 focus:ring-teal-500"
                              />
                              <Checkbox id="correspondence" className="border-gray-300" />
                              <Label htmlFor="correspondence" className="text-xs text-gray-600">
                                InCorrespondence
                              </Label>
                            </div>
                          </div>
                        </div>
                      </div>

                      <Separator />

                      <div>
                        <div className="flex items-center justify-between mb-4">
                          <h3 className="font-medium text-gray-900">Home Location</h3>
                          <div className="flex items-center space-x-2">
                            <Checkbox id="set-current" className="border-gray-300" />
                            <Label htmlFor="set-current" className="text-sm text-gray-600">
                              Set as Current Location
                            </Label>
                          </div>
                        </div>
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                          <div>
                            <Label htmlFor="home-building" className="text-sm font-medium text-gray-700">
                              Building *
                            </Label>
                            <Input
                              id="home-building"
                              defaultValue="4"
                              className="mt-1 border-gray-300 focus:border-teal-500 focus:ring-teal-500"
                            />
                          </div>
                          <div>
                            <Label htmlFor="home-street" className="text-sm font-medium text-gray-700">
                              Street *
                            </Label>
                            <Input
                              id="home-street"
                              defaultValue="28"
                              className="mt-1 border-gray-300 focus:border-teal-500 focus:ring-teal-500"
                            />
                          </div>
                        </div>
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-4">
                          <div>
                            <Label htmlFor="home-area" className="text-sm font-medium text-gray-700">
                              Area
                            </Label>
                            <Input
                              id="home-area"
                              defaultValue="ZARAJ TOWN"
                              className="mt-1 border-gray-300 focus:border-teal-500 focus:ring-teal-500"
                            />
                          </div>
                          <div>
                            <Label htmlFor="home-city" className="text-sm font-medium text-gray-700">
                              City
                            </Label>
                            <Input
                              id="home-city"
                              defaultValue="Rawalpindi"
                              className="mt-1 border-gray-300 focus:border-teal-500 focus:ring-teal-500"
                            />
                          </div>
                        </div>
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-4">
                          <div>
                            <Label htmlFor="home-country" className="text-sm font-medium text-gray-700">
                              Country
                            </Label>
                            <Input
                              id="home-country"
                              defaultValue="Pakistan"
                              className="mt-1 border-gray-300 focus:border-teal-500 focus:ring-teal-500"
                            />
                          </div>
                          <div>
                            <Label htmlFor="home-eircode" className="text-sm font-medium text-gray-700">
                              EirCode
                            </Label>
                            <div className="flex items-center space-x-2 mt-1">
                              <Input
                                id="home-eircode"
                                defaultValue="45900"
                                className="border-gray-300 focus:border-teal-500 focus:ring-teal-500"
                              />
                              <Checkbox id="home-correspondence" className="border-gray-300" />
                              <Label htmlFor="home-correspondence" className="text-xs text-gray-600">
                                InCorrespondence
                              </Label>
                            </div>
                          </div>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                </motion.div>
              </motion.div>

              <motion.div variants={itemVariants} className="mt-8 flex justify-center">
                <Button
                  onClick={() => setActiveTab(2)}
                  className="bg-teal-600 hover:bg-teal-700 text-white px-8 py-3 text-lg font-medium shadow-lg hover:shadow-xl transition-all duration-300"
                >
                  Continue to Booking & Payment
                </Button>
              </motion.div>
            </motion.div>
          )}

          {activeTab === 2 && (
            <motion.div
              key="tab2"
              variants={tabVariants}
              initial="hidden"
              animate="visible"
              exit="exit"
              transition={{ duration: 0.5 }}
            >
              <motion.div
                variants={containerVariants}
                initial="hidden"
                animate="visible"
                className="grid grid-cols-1 lg:grid-cols-3 gap-8"
              >
                <div className="lg:col-span-2 space-y-8">
                  {/* Select Clinic & Time Slot */}
                  <motion.div variants={itemVariants}>
                    <Card className="shadow-lg border-0">
                      <CardHeader className="bg-gradient-to-r from-teal-50 to-teal-100 border-b">
                        <CardTitle className="flex items-center space-x-2 text-teal-700">
                          <Building className="w-5 h-5" />
                          <span>Select Clinic & Time Slot</span>
                        </CardTitle>
                        <p className="text-sm text-gray-600">Choose from available clinics nearby</p>
                      </CardHeader>
                      <CardContent className="p-6">
                        <div>
                          <Label htmlFor="appointment-type" className="text-sm font-medium text-gray-700">
                            Appointment Type *
                          </Label>
                          <Select>
                            <SelectTrigger className="mt-1 border-gray-300 focus:border-teal-500 focus:ring-teal-500">
                              <SelectValue placeholder="Doctor Advice" />
                            </SelectTrigger>
                            <SelectContent>
                              <SelectItem value="doctor-advice">Doctor Advice</SelectItem>
                              <SelectItem value="consultation">Consultation</SelectItem>
                              <SelectItem value="follow-up">Follow-up</SelectItem>
                            </SelectContent>
                          </Select>
                        </div>

                        <div className="mt-6">
                          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                            {clinics.map((clinic) => (
                              <motion.div key={clinic.id} whileHover={{ scale: 1.02 }} whileTap={{ scale: 0.98 }}>
                                <Card
                                  className={`cursor-pointer transition-all duration-300 ${
                                    selectedClinic === clinic.id
                                      ? "ring-2 ring-teal-500 bg-teal-50"
                                      : "hover:shadow-md border-gray-200"
                                  }`}
                                  onClick={() => setSelectedClinic(clinic.id)}
                                >
                                  <CardContent className="p-4">
                                    <div className="flex items-start justify-between">
                                      <div className="flex-1">
                                        <h3 className="font-medium text-gray-900">{clinic.name}</h3>
                                        <p className="text-xs text-gray-500 mt-1">{clinic.address}</p>
                                        <Badge variant="secondary" className="mt-2 text-xs">
                                          {clinic.status}
                                        </Badge>
                                      </div>
                                      {selectedClinic === clinic.id && (
                                        <CheckCircle className="w-5 h-5 text-teal-600" />
                                      )}
                                    </div>
                                  </CardContent>
                                </Card>
                              </motion.div>
                            ))}
                          </div>
                          <div className="mt-4 flex justify-end">
                            <Button
                              variant="outline"
                              className="text-teal-600 border-teal-200 hover:bg-teal-50 bg-transparent"
                            >
                              View More →
                            </Button>
                          </div>
                        </div>

                        <div className="mt-8">
                          <h3 className="font-medium text-gray-900 mb-4">Available Time Slots for Bently:</h3>
                          <div className="mb-4">
                            <Label className="text-sm font-medium text-gray-700">Select Date</Label>
                            <div className="flex space-x-2 mt-2">
                              <Button variant="default" className="bg-teal-600 hover:bg-teal-700 text-white">
                                Sun, 27 Jul
                              </Button>
                              <Button variant="outline" className="border-gray-300 hover:bg-gray-50 bg-transparent">
                                Mon, 28 Jul
                              </Button>
                            </div>
                          </div>
                          <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-3">
                            {timeSlots.map((slot, index) => (
                              <motion.div key={slot} whileHover={{ scale: 1.05 }} whileTap={{ scale: 0.95 }}>
                                <Button
                                  variant={selectedTimeSlot === slot ? "default" : "outline"}
                                  className={`w-full text-xs py-2 transition-all duration-300 ${
                                    selectedTimeSlot === slot
                                      ? "bg-teal-600 hover:bg-teal-700 text-white shadow-md"
                                      : index === 5
                                        ? "bg-green-500 hover:bg-green-600 text-white"
                                        : "border-gray-300 hover:bg-gray-50"
                                  }`}
                                  onClick={() => setSelectedTimeSlot(slot)}
                                >
                                  {slot}
                                </Button>
                              </motion.div>
                            ))}
                          </div>
                        </div>

                        {selectedTimeSlot && (
                          <motion.div
                            initial={{ opacity: 0, y: 20 }}
                            animate={{ opacity: 1, y: 0 }}
                            className="mt-6 flex justify-center"
                          >
                            <div className="bg-green-100 border border-green-300 rounded-lg px-4 py-2 flex items-center space-x-2">
                              <Clock className="w-4 h-4 text-green-600" />
                              <span className="text-green-700 font-medium">2:35</span>
                              <Badge className="bg-green-600 text-white">Payment session active</Badge>
                            </div>
                          </motion.div>
                        )}
                      </CardContent>
                    </Card>
                  </motion.div>

                  {/* Payment Information */}
                  <motion.div variants={itemVariants}>
                    <Card className="shadow-lg border-0">
                      <CardHeader className="bg-gradient-to-r from-teal-50 to-teal-100 border-b">
                        <CardTitle className="flex items-center space-x-2 text-teal-700">
                          <CreditCard className="w-5 h-5" />
                          <span>Payment Information</span>
                        </CardTitle>
                      </CardHeader>
                      <CardContent className="p-6 space-y-6">
                        <div className="bg-gray-50 rounded-lg p-4">
                          <div className="flex justify-between items-center">
                            <span className="text-gray-700">Consultation Fee</span>
                            <span className="font-semibold text-lg">€20.00</span>
                          </div>
                        </div>

                        <div>
                          <Label htmlFor="card-details" className="text-sm font-medium text-gray-700">
                            Card Details
                          </Label>
                          <div className="mt-2 relative">
                            <Input
                              id="card-details"
                              placeholder="Card number"
                              className="border-gray-300 focus:border-teal-500 focus:ring-teal-500"
                            />
                            <div className="absolute right-3 top-3">
                              <Badge className="bg-green-600 text-white text-xs">AutoFill</Badge>
                            </div>
                          </div>
                        </div>

                        <div className="bg-gray-100 rounded-lg p-4 text-center">
                          <div className="flex items-center justify-center space-x-2 text-gray-600">
                            <span>💳</span>
                            <span className="font-medium">Pay €20.00</span>
                          </div>
                          <p className="text-xs text-gray-500 mt-2">
                            🔒 Secured by Stripe • Your payment information is encrypted
                          </p>
                        </div>
                      </CardContent>
                    </Card>
                  </motion.div>
                </div>

                {/* Booking Summary */}
                <motion.div variants={itemVariants}>
                  <Card className="shadow-lg border-0 sticky top-8">
                    <CardHeader className="bg-gradient-to-r from-teal-50 to-teal-100 border-b">
                      <CardTitle className="text-teal-700">Booking Summary</CardTitle>
                    </CardHeader>
                    <CardContent className="p-6 space-y-4">
                      <div className="space-y-3">
                        <div className="flex items-center space-x-3">
                          <User className="w-4 h-4 text-gray-500" />
                          <span className="text-sm">Patient: Hamza Arshad</span>
                        </div>
                        <div className="flex items-center space-x-3">
                          <Calendar className="w-4 h-4 text-gray-500" />
                          <span className="text-sm">Type: Doctor Advice</span>
                        </div>
                        <div className="flex items-center space-x-3">
                          <Building className="w-4 h-4 text-gray-500" />
                          <span className="text-sm">Clinic: Bently</span>
                        </div>
                        <div className="flex items-center space-x-3">
                          <Clock className="w-4 h-4 text-gray-500" />
                          <span className="text-sm">Time: 09:40 - 09:50</span>
                        </div>
                        <div className="flex items-center space-x-3">
                          <Mail className="w-4 h-4 text-gray-500" />
                          <span className="text-sm">Email: <EMAIL></span>
                        </div>
                        <div className="flex items-center space-x-3">
                          <Phone className="w-4 h-4 text-gray-500" />
                          <span className="text-sm">Phone: +353083123467</span>
                        </div>
                      </div>

                      <Separator />

                      <div className="flex justify-between items-center">
                        <span className="font-semibold text-lg">Total:</span>
                        <span className="font-bold text-xl text-teal-600">€20</span>
                      </div>
                    </CardContent>
                  </Card>
                </motion.div>
              </motion.div>

              <motion.div variants={itemVariants} className="mt-8 flex justify-center">
                <Button
                  onClick={() => setActiveTab(1)}
                  variant="outline"
                  className="mr-4 border-gray-300 hover:bg-gray-50"
                >
                  Back to Details
                </Button>
              </motion.div>
            </motion.div>
          )}
        </AnimatePresence>
      </div>
    </div>
  )
}
